🚀 WINDOWS SERVER 2019 REDİS KURULUMU - POWERSHELL KOMUTLARI
================================================================

Bu dosyadaki komutları sıra sıra PowerShell'e (Administrator) kopyalayıp yapı<PERSON>ı<PERSON>ın.
Her adımdan sonra sonucu kontrol edin.

================================================================
ADIM 1: DOCKER ENGINE KURULUMU
================================================================

# 1.1) Windows özelliklerini etkinleştir
Enable-WindowsOptionalFeature -Online -FeatureName containers -All
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# Sistemi yeniden başlatın (bu komut sistemi yeniden başlatır)
Restart-Computer

# ⚠️ SISTEM YENİDEN BAŞLADIKTAN SONRA DEVAM EDİN

# 1.2) Docker Engine kurulumu
Install-Module -Name DockerMsftProvider -Repository PSGallery -Force
Install-Package -Name docker -ProviderName DockerMsftProvider -Force
Start-Service Docker
Set-Service -Name Docker -StartupType Automatic

# 1.3) Docker'ın çalıştığını kontrol et
docker version

# 1.4) Docker Compose kurulumu
New-Item -ItemType Directory -Path "$Env:ProgramFiles\Docker" -Force
Invoke-WebRequest "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-windows-x86_64.exe" -UseBasicParsing -OutFile "$Env:ProgramFiles\Docker\docker-compose.exe"

# PATH'e ekle
$env:PATH += ";$Env:ProgramFiles\Docker"
[Environment]::SetEnvironmentVariable("Path", $env:PATH, [EnvironmentVariableTarget]::Machine)

# Test et
docker-compose --version

================================================================
ADIM 2: GYMPROJECT KLASÖR YAPISINI OLUŞTUR
================================================================

# Ana klasörü oluştur
New-Item -ItemType Directory -Path "C:\GymProject" -Force
New-Item -ItemType Directory -Path "C:\GymProject\redis" -Force
New-Item -ItemType Directory -Path "C:\GymProject\data" -Force
New-Item -ItemType Directory -Path "C:\GymProject\logs" -Force
New-Item -ItemType Directory -Path "C:\GymProject\backup" -Force
New-Item -ItemType Directory -Path "C:\GymProject\monitoring" -Force

# Klasöre git
Set-Location "C:\GymProject"

================================================================
ADIM 3: REDIS CONFIGURATION DOSYASI OLUŞTUR
================================================================

# Redis konfigürasyon dosyasını oluştur
@"
# Redis Production Configuration for GymProject
bind 0.0.0.0
port 6379
protected-mode yes
requirepass GymProject2024Redis!Strong
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
loglevel notice
logfile /var/log/redis/redis-server.log
tcp-keepalive 300
timeout 300
tcp-backlog 511
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c"
databases 16
slowlog-log-slower-than 10000
slowlog-max-len 128
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
"@ | Out-File -FilePath "C:\GymProject\redis\redis.conf" -Encoding UTF8

================================================================
ADIM 4: DOCKER COMPOSE DOSYASI OLUŞTUR
================================================================

@"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymproject-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymProject2024Redis!Strong
    networks:
      - gymproject-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: gymproject-redis-ui
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:GymProject2024Redis!Strong
    networks:
      - gymproject-network
    depends_on:
      - redis

volumes:
  redis_data:
    driver: local

networks:
  gymproject-network:
    driver: bridge
"@ | Out-File -FilePath "C:\GymProject\docker-compose.yml" -Encoding UTF8

================================================================
ADIM 5: REDIS CONTAINER'LARI BAŞLAT
================================================================

# Docker image'ları indir ve container'ları başlat
docker-compose up -d

# Container durumunu kontrol et
docker-compose ps

# Redis loglarını kontrol et
docker-compose logs redis

# Redis Commander loglarını kontrol et
docker-compose logs redis-commander

================================================================
ADIM 6: REDIS BAĞLANTI TESTİ
================================================================

# Redis CLI ile bağlantı testi
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" ping

# Test verileri ekle
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" set test "Hello GymProject"
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" get test

# Memory bilgilerini kontrol et
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" info memory

================================================================
ADIM 7: FIREWALL KURALLARI EKLE
================================================================

# Redis portu için firewall kuralı
New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow

# Redis Commander UI portu
New-NetFirewallRule -DisplayName "Redis Commander" -Direction Inbound -Protocol TCP -LocalPort 8081 -Action Allow

================================================================
ADIM 8: BACKUP SCRIPT'İ OLUŞTUR
================================================================

@"
param(
    [string]`$BackupPath = "C:\GymProject\backup",
    [int]`$RetentionDays = 7
)

`$Date = Get-Date -Format "yyyyMMdd_HHmmss"
`$BackupFile = "`$BackupPath\redis-backup-`$Date.rdb"

Write-Host "Redis backup başlatılıyor: `$BackupFile"

docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" BGSAVE
Start-Sleep -Seconds 10
docker cp gymproject-redis-prod:/data/dump.rdb `$BackupFile

Get-ChildItem `$BackupPath -Name "redis-backup-*.rdb" | 
    Where-Object { (Get-Date) - (Get-Item `$_).CreationTime -gt (New-TimeSpan -Days `$RetentionDays) } |
    Remove-Item

Write-Host "Backup tamamlandı: `$BackupFile"
"@ | Out-File -FilePath "C:\GymProject\backup\redis-backup.ps1" -Encoding UTF8

================================================================
ADIM 9: MONITORING SCRIPT'İ OLUŞTUR
================================================================

@"
`$RedisContainer = "gymproject-redis-prod"
`$LogFile = "C:\GymProject\logs\redis-health.log"

function Write-Log {
    param([string]`$Message)
    `$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "`$Timestamp - `$Message" | Out-File -FilePath `$LogFile -Append
}

`$ContainerStatus = docker inspect --format='{{.State.Status}}' `$RedisContainer 2>`$null

if (`$ContainerStatus -eq "running") {
    `$PingResult = docker exec `$RedisContainer redis-cli -a "GymProject2024Redis!Strong" ping 2>`$null
    
    if (`$PingResult -eq "PONG") {
        Write-Log "Redis sağlıklı - Container: `$ContainerStatus, Ping: `$PingResult"
        `$MemoryInfo = docker exec `$RedisContainer redis-cli -a "GymProject2024Redis!Strong" info memory
        Write-Log "Memory Info: `$MemoryInfo"
        exit 0
    } else {
        Write-Log "HATA: Redis ping başarısız - `$PingResult"
        exit 1
    }
} else {
    Write-Log "HATA: Redis container çalışmıyor - Status: `$ContainerStatus"
    Write-Log "Redis container yeniden başlatılıyor..."
    docker-compose -f "C:\GymProject\docker-compose.yml" restart redis
    exit 1
}
"@ | Out-File -FilePath "C:\GymProject\monitoring\redis-health.ps1" -Encoding UTF8

================================================================
ADIM 10: SCHEDULED TASK'LER OLUŞTUR
================================================================

# Günlük backup task'i
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\backup\redis-backup.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "02:00AM"
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Hours 1)
Register-ScheduledTask -TaskName "Redis Daily Backup" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

# Health check task'i (5 dakikada bir)
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\monitoring\redis-health.ps1"
$Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365)
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Minutes 2)
Register-ScheduledTask -TaskName "Redis Health Check" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

================================================================
ADIM 11: MANUEL BACKUP TESTİ
================================================================

# Backup script'ini test et
PowerShell.exe -File "C:\GymProject\backup\redis-backup.ps1"

# Health check script'ini test et
PowerShell.exe -File "C:\GymProject\monitoring\redis-health.ps1"

# Backup dosyalarını kontrol et
Get-ChildItem "C:\GymProject\backup" -Name "*.rdb"

================================================================
ADIM 12: FINAL KONTROLLER
================================================================

# Container'ların durumunu kontrol et
docker ps

# Redis bağlantısını test et
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" info server

# Disk kullanımını kontrol et
Get-ChildItem "C:\GymProject" -Recurse | Measure-Object -Property Length -Sum

# Scheduled task'lerin durumunu kontrol et
Get-ScheduledTask -TaskName "Redis*"

Write-Host "✅ Redis kurulumu tamamlandı!"
Write-Host "🌐 Redis Commander: http://localhost:8081"
Write-Host "🔗 Redis Connection: localhost:6379"
Write-Host "🔑 Password: GymProject2024Redis!Strong"
