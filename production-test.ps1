# 🚀 PRODUCTION REDIS TEST SCRIPT
# Bu script'i Windows Server'da Redis kurulumundan sonra çalıştırın

Write-Host "🔍 GymProject Production Redis Test Başlatılıyor..." -ForegroundColor Green

# Test 1: Docker Container Durumu
Write-Host "`n1️⃣ Docker Container Durumu Kontrol Ediliyor..." -ForegroundColor Yellow
$containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
Write-Host $containers

# Test 2: Redis Ping Testi
Write-Host "`n2️⃣ Redis Ping Testi..." -ForegroundColor Yellow
try {
    $pingResult = docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" ping
    if ($pingResult -eq "PONG") {
        Write-Host "✅ Redis Ping Başarılı: $pingResult" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis Ping Başarısız: $pingResult" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Redis Ping Hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Redis Memory Bilgisi
Write-Host "`n3️⃣ Redis Memory Kullanımı..." -ForegroundColor Yellow
try {
    $memoryInfo = docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" info memory | Select-String "used_memory_human", "maxmemory_human", "used_memory_peak_human"
    $memoryInfo | ForEach-Object { Write-Host "📊 $_" -ForegroundColor Cyan }
} catch {
    Write-Host "❌ Memory bilgisi alınamadı: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Redis Test Verileri
Write-Host "`n4️⃣ Redis Test Verileri..." -ForegroundColor Yellow
try {
    # Test verisi ekle
    docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" set "test:gymproject" "Production Redis Çalışıyor!"
    
    # Test verisini oku
    $testValue = docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" get "test:gymproject"
    Write-Host "✅ Test Verisi: $testValue" -ForegroundColor Green
    
    # Test verisini sil
    docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" del "test:gymproject"
    Write-Host "✅ Test verisi temizlendi" -ForegroundColor Green
} catch {
    Write-Host "❌ Test verisi hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Redis Commander UI Kontrolü
Write-Host "`n5️⃣ Redis Commander UI Kontrolü..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Redis Commander UI Erişilebilir: http://localhost:8081" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Redis Commander UI Erişilemiyor: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Backup Script Testi
Write-Host "`n6️⃣ Backup Script Testi..." -ForegroundColor Yellow
try {
    if (Test-Path "C:\GymProject\backup\redis-backup.ps1") {
        Write-Host "✅ Backup script mevcut" -ForegroundColor Green
        
        # Test backup çalıştır
        & "C:\GymProject\backup\redis-backup.ps1"
        
        # Backup dosyasını kontrol et
        $backupFiles = Get-ChildItem "C:\GymProject\backup" -Name "*.rdb" | Sort-Object CreationTime -Descending | Select-Object -First 1
        if ($backupFiles) {
            Write-Host "✅ Son backup dosyası: $backupFiles" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Backup script bulunamadı" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Backup test hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Health Check Script Testi
Write-Host "`n7️⃣ Health Check Script Testi..." -ForegroundColor Yellow
try {
    if (Test-Path "C:\GymProject\monitoring\redis-health.ps1") {
        Write-Host "✅ Health check script mevcut" -ForegroundColor Green
        
        # Health check çalıştır
        & "C:\GymProject\monitoring\redis-health.ps1"
        
        # Log dosyasını kontrol et
        if (Test-Path "C:\GymProject\logs\redis-health.log") {
            $lastLog = Get-Content "C:\GymProject\logs\redis-health.log" | Select-Object -Last 1
            Write-Host "📝 Son health check: $lastLog" -ForegroundColor Cyan
        }
    } else {
        Write-Host "❌ Health check script bulunamadı" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Health check test hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Scheduled Task Kontrolü
Write-Host "`n8️⃣ Scheduled Task Kontrolü..." -ForegroundColor Yellow
try {
    $redisTasks = Get-ScheduledTask -TaskName "Redis*" -ErrorAction SilentlyContinue
    if ($redisTasks) {
        $redisTasks | ForEach-Object {
            Write-Host "✅ Task: $($_.TaskName) - State: $($_.State)" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Redis scheduled task'leri bulunamadı" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Scheduled task kontrolü hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Firewall Kuralları Kontrolü
Write-Host "`n9️⃣ Firewall Kuralları Kontrolü..." -ForegroundColor Yellow
try {
    $redisRule = Get-NetFirewallRule -DisplayName "Redis Server" -ErrorAction SilentlyContinue
    $commanderRule = Get-NetFirewallRule -DisplayName "Redis Commander" -ErrorAction SilentlyContinue
    
    if ($redisRule) {
        Write-Host "✅ Redis Server firewall kuralı aktif" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis Server firewall kuralı bulunamadı" -ForegroundColor Red
    }
    
    if ($commanderRule) {
        Write-Host "✅ Redis Commander firewall kuralı aktif" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis Commander firewall kuralı bulunamadı" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Firewall kontrolü hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Disk Kullanımı
Write-Host "`n🔟 Disk Kullanımı Kontrolü..." -ForegroundColor Yellow
try {
    $gymProjectSize = Get-ChildItem "C:\GymProject" -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum
    $sizeInMB = [math]::Round($gymProjectSize.Sum / 1MB, 2)
    Write-Host "📁 GymProject klasörü boyutu: $sizeInMB MB" -ForegroundColor Cyan
    
    # Docker volume boyutu
    $dockerVolumes = docker volume ls --format "table {{.Name}}\t{{.Size}}" 2>$null
    Write-Host "🐳 Docker Volumes:" -ForegroundColor Cyan
    Write-Host $dockerVolumes
} catch {
    Write-Host "❌ Disk kullanımı kontrolü hatası: $($_.Exception.Message)" -ForegroundColor Red
}

# Özet Rapor
Write-Host "`n📋 TEST ÖZET RAPORU" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor Magenta
Write-Host "✅ Redis Production Kurulumu Tamamlandı" -ForegroundColor Green
Write-Host "🌐 Redis Commander UI: http://localhost:8081" -ForegroundColor Cyan
Write-Host "🔗 Redis Connection: localhost:6379" -ForegroundColor Cyan
Write-Host "🔑 Password: GymProject2024Redis!Strong" -ForegroundColor Cyan
Write-Host "📁 Backup Klasörü: C:\GymProject\backup" -ForegroundColor Cyan
Write-Host "📊 Log Klasörü: C:\GymProject\logs" -ForegroundColor Cyan

Write-Host "`n🎯 SONRAKI ADIMLAR:" -ForegroundColor Yellow
Write-Host "1. .NET uygulamanızı 'canlı' environment ile başlatın" -ForegroundColor White
Write-Host "2. API endpoint'lerini test edin" -ForegroundColor White
Write-Host "3. Cache performance'ını ölçün" -ForegroundColor White
Write-Host "4. Production monitoring'i aktifleştirin" -ForegroundColor White

Write-Host "`n🚀 Production Redis Kurulumu Başarıyla Tamamlandı!" -ForegroundColor Green
