🚀 WINDOWS SERVER 2019 REDİS KURULUM REHBERİ
================================================================

SUNUCU BİLGİLERİ:
- OS: Windows Server 2019
- RAM: 4GB (Redis için 2GB ayrılacak)
- CPU: 2 Core
- Mevcut Kullanım: 1 salon, 100 üye
- Hedef: Ölçeklenebilir altyapı

KURULUM SIRASI:
1. Docker Engine Kurulumu (Docker Desktop alternatifi)
2. Redis Container Deployment
3. Production Configuration
4. Monitoring & Backup Setup
5. .NET Connection Test

================================================================
ADIM 1: DOCKER ENGINE KURULUMU (DÜZELTME)
================================================================

Windows Server 2019'da Docker kurulumu için alternatif yöntem kullanacağız.

1.1) PowerShell'i Administrator olarak açın ve şu komutları çalıştırın:

# Windows özelliklerini etkinleştir
Enable-WindowsOptionalFeature -Online -FeatureName containers -All
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All

# Sistemi yeniden başlatın (gerekirse)
# Restart-Computer

1.2) Docker Desktop for Windows Server kurulumu:

# Chocolatey package manager'ı kur (Docker kurulumu için)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Docker'ı Chocolatey ile kur
choco install docker-desktop -y

# Alternatif: Manuel Docker kurulumu
# Docker installer'ı indir
$dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
$dockerInstaller = "$env:TEMP\DockerDesktopInstaller.exe"
Invoke-WebRequest -Uri $dockerUrl -OutFile $dockerInstaller

# Docker'ı sessiz kurulum ile kur
Start-Process -FilePath $dockerInstaller -ArgumentList "install --quiet" -Wait

1.3) Docker Compose'u yükleyin:

# Docker Compose'u indir
New-Item -ItemType Directory -Path "$Env:ProgramFiles\Docker" -Force
Invoke-WebRequest "https://github.com/docker/compose/releases/download/v2.21.0/docker-compose-windows-x86_64.exe" -UseBasicParsing -OutFile "$Env:ProgramFiles\Docker\docker-compose.exe"

# PATH'e ekle
$env:PATH += ";$Env:ProgramFiles\Docker"
[Environment]::SetEnvironmentVariable("Path", $env:PATH, [EnvironmentVariableTarget]::Machine)

# Test et
docker-compose --version

================================================================
ADIM 2: GYMPROJECT KLASÖR YAPISINI HAZIRLA
================================================================

2.1) GymProject klasörünü oluşturun:

# Ana klasörü oluştur
New-Item -ItemType Directory -Path "C:\GymProject" -Force
Set-Location "C:\GymProject"

# Alt klasörleri oluştur
New-Item -ItemType Directory -Path "C:\GymProject\redis" -Force
New-Item -ItemType Directory -Path "C:\GymProject\data" -Force
New-Item -ItemType Directory -Path "C:\GymProject\logs" -Force
New-Item -ItemType Directory -Path "C:\GymProject\backup" -Force

================================================================
ADIM 3: PRODUCTION REDIS CONFIGURATION
================================================================

3.1) Redis konfigürasyon dosyasını oluşturun:

# C:\GymProject\redis\redis.conf dosyasını oluştur
@"
# Redis Production Configuration for GymProject
# Windows Server 2019 - 4GB RAM, 2CPU

# Network
bind 0.0.0.0
port 6379
protected-mode yes

# Authentication
requirepass GymProject2024Redis!Strong

# Memory Management (2GB max)
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# AOF Persistence
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance
tcp-keepalive 300
timeout 300
tcp-backlog 511

# Security
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_b835729c9c"

# Database
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
"@ | Out-File -FilePath "C:\GymProject\redis\redis.conf" -Encoding UTF8

================================================================
ADIM 4: DOCKER COMPOSE PRODUCTION SETUP
================================================================

4.1) Production docker-compose.yml oluşturun:

@"
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: gymproject-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ./logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=GymProject2024Redis!Strong
    networks:
      - gymproject-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: gymproject-redis-ui
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:GymProject2024Redis!Strong
    networks:
      - gymproject-network
    depends_on:
      - redis

volumes:
  redis_data:
    driver: local

networks:
  gymproject-network:
    driver: bridge
"@ | Out-File -FilePath "C:\GymProject\docker-compose.yml" -Encoding UTF8

================================================================
ADIM 5: REDIS CONTAINER'I BAŞLAT
================================================================

5.1) Redis'i başlatın:

# GymProject klasörüne git
Set-Location "C:\GymProject"

# Container'ları başlat
docker-compose up -d

# Durumu kontrol et
docker-compose ps

# Logları kontrol et
docker-compose logs redis

5.2) Redis bağlantısını test edin:

# Redis CLI ile test
docker exec -it gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong"

# Test komutları (Redis CLI içinde):
# ping
# set test "Hello GymProject"
# get test
# exit

================================================================
ADIM 6: WINDOWS FIREWALL AYARLARI
================================================================

6.1) Firewall kurallarını ekleyin:

# Redis portu için firewall kuralı
New-NetFirewallRule -DisplayName "Redis Server" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow

# Redis Commander UI portu
New-NetFirewallRule -DisplayName "Redis Commander" -Direction Inbound -Protocol TCP -LocalPort 8081 -Action Allow

================================================================
ADIM 7: MONITORING VE BACKUP SCRIPT'LERİ
================================================================

7.1) Backup script'i oluşturun:

@"
# Redis Backup Script
# C:\GymProject\backup\redis-backup.ps1

param(
    [string]$BackupPath = "C:\GymProject\backup",
    [int]$RetentionDays = 7
)

$Date = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupFile = "$BackupPath\redis-backup-$Date.rdb"

Write-Host "Redis backup başlatılıyor: $BackupFile"

# Redis BGSAVE komutu
docker exec gymproject-redis-prod redis-cli -a "GymProject2024Redis!Strong" BGSAVE

# Backup dosyasını kopyala
Start-Sleep -Seconds 10
docker cp gymproject-redis-prod:/data/dump.rdb $BackupFile

# Eski backup'ları temizle
Get-ChildItem $BackupPath -Name "redis-backup-*.rdb" | 
    Where-Object { (Get-Date) - (Get-Item $_).CreationTime -gt (New-TimeSpan -Days $RetentionDays) } |
    Remove-Item

Write-Host "Backup tamamlandı: $BackupFile"
"@ | Out-File -FilePath "C:\GymProject\backup\redis-backup.ps1" -Encoding UTF8

7.2) Monitoring script'i oluşturun:

@"
# Redis Health Check Script
# C:\GymProject\monitoring\redis-health.ps1

$RedisContainer = "gymproject-redis-prod"
$LogFile = "C:\GymProject\logs\redis-health.log"

function Write-Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$Timestamp - $Message" | Out-File -FilePath $LogFile -Append
}

# Container durumunu kontrol et
$ContainerStatus = docker inspect --format='{{.State.Status}}' $RedisContainer 2>$null

if ($ContainerStatus -eq "running") {
    # Redis ping testi
    $PingResult = docker exec $RedisContainer redis-cli -a "GymProject2024Redis!Strong" ping 2>$null
    
    if ($PingResult -eq "PONG") {
        Write-Log "Redis sağlıklı - Container: $ContainerStatus, Ping: $PingResult"
        
        # Memory kullanımını kontrol et
        $MemoryInfo = docker exec $RedisContainer redis-cli -a "GymProject2024Redis!Strong" info memory
        Write-Log "Memory Info: $MemoryInfo"
        
        exit 0
    } else {
        Write-Log "HATA: Redis ping başarısız - $PingResult"
        exit 1
    }
} else {
    Write-Log "HATA: Redis container çalışmıyor - Status: $ContainerStatus"
    
    # Container'ı yeniden başlat
    Write-Log "Redis container yeniden başlatılıyor..."
    docker-compose -f "C:\GymProject\docker-compose.yml" restart redis
    
    exit 1
}
"@ | Out-File -FilePath "C:\GymProject\monitoring\redis-health.ps1" -Encoding UTF8

================================================================
ADIM 8: SCHEDULED TASK'LER OLUŞTUR
================================================================

8.1) Backup için scheduled task:

# Günlük backup task'i oluştur
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\backup\redis-backup.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "02:00AM"
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Hours 1)
Register-ScheduledTask -TaskName "Redis Daily Backup" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

8.2) Health check için scheduled task:

# 5 dakikada bir health check
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GymProject\monitoring\redis-health.ps1"
$Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date) -RepetitionInterval (New-TimeSpan -Minutes 5) -RepetitionDuration (New-TimeSpan -Days 365)
$Settings = New-ScheduledTaskSettingsSet -ExecutionTimeLimit (New-TimeSpan -Minutes 2)
Register-ScheduledTask -TaskName "Redis Health Check" -Action $Action -Trigger $Trigger -Settings $Settings -User "SYSTEM"

================================================================
SONRAKI ADIMLAR
================================================================

1. Bu script'i çalıştırdıktan sonra Redis Commander'a erişin: http://[SUNUCU-IP]:8081
2. .NET uygulamanızın appsettings.json'ında connection string'i güncelleyin
3. Production test'lerini yapın
4. Performance monitoring'i aktifleştirin

BAĞLANTI STRING'İ:
"Redis": {
  "Production": "[SUNUCU-IP]:6379,password=GymProject2024Redis!Strong,ssl=false,abortConnect=false"
}
